const mongoose = require('mongoose');
const config = require('../../config/config');
const { AssemblyBom } = require('..');
const { Product } = require('../inventory');
const logger = require('../../config/logger');

const removeBomComments = async () => {
  try {
    logger.info('Migration started');
    await mongoose.connect(config.mongoose.url, {
      ...config.mongoose.options,
      useCreateIndex: true,
    });
    const prods = await Product.find({
      $or: [{ children: { $exists: false } }, { children: { $size: 0 } }],
    });

    for (let i = 0; i < prods.length; i++) {
      const pro = prods[i];
      for (let ii = 0; ii < pro?.productionDetails?.bom?.length; ii++) {
        const bom = pro?.productionDetails?.bom?.[ii];
        await AssemblyBom.findByIdAndUpdate(bom?._id, { bomProduct: pro?._id });
        logger.info(`Bom ${bom?._id} updated`);
      }
    }

    const boms = await AssemblyBom.find({});
    for (let i = 0; i < boms.length; i++) {
      const bom = boms[i];

      let idx = bom?.rowId ? bom?.rowId?.split('.')?.length : 0;

      bom.variantIndex = idx;
      await bom.save();
      logger.info(`Bom ${bom?._id} variantIndex added`);
    }

    logger.info('Migration completed');
  } catch (err) {
    logger.error(err);
  } finally {
    mongoose.disconnect();
    process.exit(0);
  }
};
removeBomComments();
