const httpStatus = require('http-status');
const catchAsync = require('../../utils/catchAsync');
const pick = require('../../utils/pick');
const { ledgerService } = require('../../services');

const paginateLedger = catchAsync(async (req, res) => {
  const options = pick(req.query, ['limit', 'page']);
  const pos = await ledgerService.getPaginatedLedgers(
    req.query.filters,
    options,
    req.query.field,
    req.query.type,
    req.profile,
    req.query.searchTerm
  );
  res.status(httpStatus.OK).send(pos);
});

const createLedger = catchAsync(async (req, res) => {
  const newCreate = await ledgerService.createLedger(req.body, req.profile);

  res.status(httpStatus.CREATED).send(newCreate);
});

const editLedger = catchAsync(async (req, res) => {
  const newCreate = await ledgerService.editLedger(req.body, req.profile);

  res.status(httpStatus.CREATED).send(newCreate);
});

const deleteLedger = catchAsync(async (req, res) => {
  const newCreate = await ledgerService.deleteLedger(req.body);

  res.status(httpStatus.CREATED).send(newCreate);
});

const deleteManyLedger = catchAsync(async (req, res) => {
  const newCreate = await ledgerService.deleteManyLedger(req.body);

  res.status(httpStatus.CREATED).send(newCreate);
});

const getLedgerById = catchAsync(async (req, res) => {
  const ledger = await ledgerService.getLedgerById(req.params.id, req.profile);
  res.status(httpStatus.OK).send(ledger);
});

const getFilterOptions = catchAsync(async (req, res) => {
  const filterOptions = await ledgerService.getFilterOptions(req.profile);
  res.status(httpStatus.OK).send(filterOptions);
});

const createLedgerName = catchAsync(async (req, res) => {
  const newLedgerName = await ledgerService.createLedgerName(
    req.profile,
    req.body
  );
  res.status(httpStatus.CREATED).send(newLedgerName);
});

const getLedgerNameForOptions = catchAsync(async (req, res) => {
  const options = await ledgerService.getLedgerNameForOptions(req.profile);
  res.status(httpStatus.OK).send(options);
});

const deleteLedgerName = catchAsync(async (req, res) => {
  const result = await ledgerService.deleteLedgerName(req.params.id);
  res.status(httpStatus.OK).send(result);
});

const editLedgerName = catchAsync(async (req, res) => {
  const result = await ledgerService.editLedgerName(req.params.id, req.body);
  res.status(httpStatus.OK).send(result);
});

module.exports = {
  paginateLedger,
  createLedger,
  editLedger,
  deleteLedger,
  deleteManyLedger,
  getLedgerById,
  getFilterOptions,
  createLedgerName,
  getLedgerNameForOptions,
  deleteLedgerName,
  editLedgerName,
};
