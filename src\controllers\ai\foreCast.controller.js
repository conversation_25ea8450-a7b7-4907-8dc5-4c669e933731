const httpStatus = require('http-status');
const catchAsync = require('../../utils/catchAsync');
const { foreCastService } = require('../../services/ai');
const dataGenerator = require('../../utils/chatBotDataGen');

const getForecastPart = catchAsync(async (req, res) => {
  const forecastData = await foreCastService.pythonRedirect(
    req.query.name,
    req.query.period || 90,
    req.query.insight,
    req.profile
  );
  res.status(httpStatus.OK).send(
    req.isChatBot
      ? dataGenerator({
          data: forecastData,
          success: forecastData ? 'true' : 'false',
        })
      : forecastData
  );
});

const getForecastSales = catchAsync(async (req, res) => {
  const salesDataForForecast = await foreCastService.salesForecast(
    req.query.period,
    req.profile,
    req.isChatBot
  );
  res.status(httpStatus.OK).send(
    req.isChatBot
      ? dataGenerator({
          success:
            salesDataForForecast && !salesDataForForecast.msg
              ? 'true'
              : 'false',
          data: salesDataForForecast,
          message: salesDataForForecast.msg,
        })
      : salesDataForForecast
  );
});

const getSalesInsight = catchAsync(async (req, res) => {
  const infoTiles = await foreCastService.getInfoTile(req.profile);
  res.status(httpStatus.OK).send(
    dataGenerator({
      salesOrder: infoTiles.salesOrder,
      salesQuotation: infoTiles.salesQuotation,
      salesInquiry: infoTiles.salesInquiry,
      status: infoTiles ? 'success' : 'failed',
    })
  );
});

module.exports = { getForecastPart, getForecastSales, getSalesInsight };
