const httpStatus = require('http-status');
const catchAsync = require('../../utils/catchAsync');
const pick = require('../../utils/pick');
const { billsService } = require('../../services');

const queryBills = catchAsync(async (req, res) => {
  const options = pick(req.query, ['page', 'limit']);
  const { searchTerm, type } = req.query;
  const matchStage = {
    profileId: req?.profile?._id,
    archived: { $ne: true },
    type,
  };

  const bills = await billsService.queryBills(matchStage, options, searchTerm);

  res.status(httpStatus.OK).send(bills);
});

const getBillById = catchAsync(async (req, res) => {
  const bill = await billsService.getBillById(req.params.id);
  res.status(httpStatus.OK).send(bill);
});

const createBill = catchAsync(async (req, res) => {
  const bill = await billsService.createBill(req.body, req.profile._id);
  res.status(httpStatus.CREATED).send(bill);
});

const updateBill = catchAsync(async (req, res) => {
  const bill = await billsService.updateBill(req.params.id, req.body);
  res.status(httpStatus.OK).send(bill);
});

const deleteBill = catchAsync(async (req, res) => {
  await billsService.deleteBill(req.params.id);
  res.status(httpStatus.NO_CONTENT).send();
});

const deleteManyBills = catchAsync(async (req, res) => {
  const { ids } = req.body;
  const result = await billsService.deleteManyBills(ids);
  res.status(httpStatus.OK).send({
    message: `${result.deletedCount} bills deleted successfully`,
    deletedCount: result.deletedCount,
  });
});

const updateBillStatus = catchAsync(async (req, res) => {
  const { status } = req.body;
  const bill = await billsService.updateBillStatus(req.params.id, status);
  res.status(httpStatus.OK).send(bill);
});

const getBillsMetrics = catchAsync(async (req, res) => {
  const metrics = await billsService.getBillsMetrics(req.profile._id);
  res.status(httpStatus.OK).send(metrics);
});

module.exports = {
  queryBills,
  getBillById,
  createBill,
  updateBill,
  deleteBill,
  deleteManyBills,
  updateBillStatus,
  getBillsMetrics,
};
