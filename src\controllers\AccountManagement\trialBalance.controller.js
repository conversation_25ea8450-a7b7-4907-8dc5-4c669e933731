const httpStatus = require('http-status');
const catchAsync = require('../../utils/catchAsync');
const pick = require('../../utils/pick');
const { trialBalanceService } = require('../../services');

const parseDateRange = (query) => {
  const { dateRange, startDate, endDate } = query;
  if (dateRange) {
    try {
      const parsed = JSON.parse(dateRange);
      if (parsed.startDate && parsed.endDate) {
        return [parsed.startDate, parsed.endDate];
      }
    } catch (error) {
      return null;
    }
  } else if (startDate && endDate) {
    return [startDate, endDate];
  }

  return null;
};
const getTrialBalance = catchAsync(async (req, res) => {
  const options = pick(req.query, ['limit', 'page']);
  const parsedDateRange = parseDateRange(req.query);
  const trailBalance = await trialBalanceService.getTrialBalance(
    req.profile,
    req.query.searchTerm,
    parsedDateRange,
    options
  );
  res.status(httpStatus.OK).send(trailBalance);
});

const getTrialBalanceSummary = catchAsync(async (req, res) => {
  const summary = await trialBalanceService.getTrialBalanceSummary(req.profile);
  res.status(httpStatus.OK).send(summary);
});

const getTrialBalanceByLedgerNameId = catchAsync(async (req, res) => {
  const trailBalance = await trialBalanceService.getTrialBalanceByLedgerNameId(
    req.profile,
    req.params.ledgerNameId
  );
  res.status(httpStatus.OK).send(trailBalance);
});

module.exports = {
  getTrialBalance,
  getTrialBalanceSummary,
  getTrialBalanceByLedgerNameId,
};
