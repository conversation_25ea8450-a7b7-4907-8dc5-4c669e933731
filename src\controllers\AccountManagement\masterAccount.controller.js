const httpStatus = require('http-status');
const catchAsync = require('../../utils/catchAsync');
const pick = require('../../utils/pick');
const { masterAccountService } = require('../../services');

// const paginateMasterAccounts = catchAsync(async (req, res) => {
//   const options = pick(req.query, ['limit', 'page']);
//   const leads = await masterAccountService.getPaginatedMastersAccounts(
//     req.query.filter_name,
//     req.query.filter_value,
//     options,
//     req.query.field,
//     req.profile,
//     req.query.type,
//     req.query.searchTerm,
//     req.query.timeRange,
//     req.query.dateRange
//   );
//   res.status(httpStatus.OK).send(leads);
// });

const paginateMasterAccounts = catchAsync(async (req, res) => {
  const options = pick(req.query, ['limit', 'page']);
  const pos = await masterAccountService.getPaginatedMastersAccounts(
    req.query.filters,
    options,
    req.query.field,
    req.query.type,
    req.profile,
    req.query.searchTerm
  );
  res.status(httpStatus.OK).send(pos);
});

const createMasterAccount = catchAsync(async (req, res) => {
  const newCreate = await masterAccountService.createMasterAccount(
    req.body,
    req.profile
  );

  res.status(httpStatus.CREATED).send(newCreate);
});

const editMasterAccount = catchAsync(async (req, res) => {
  const newCreate = await masterAccountService.editMasterAccount(
    req.body,
    req.profile
  );

  res.status(httpStatus.CREATED).send(newCreate);
});

const deleteMasterAccount = catchAsync(async (req, res) => {
  const newCreate = await masterAccountService.deleteMasterAccount(req.body);

  res.status(httpStatus.CREATED).send(newCreate);
});

const deleteManyMasterAccount = catchAsync(async (req, res) => {
  const newCreate = await masterAccountService.deleteManyMasterAccounts(
    req.body
  );

  res.status(httpStatus.CREATED).send(newCreate);
});

const getAccountForOptions = catchAsync(async (req, res) => {
  const accounts = await masterAccountService.getAccountForOptions(req.profile);
  res.status(httpStatus.OK).send(accounts);
});

const createCustomTypes = catchAsync(async (req, res) => {
  const newCreate = await masterAccountService.createCustomType(
    req.body,
    req.profile
  );

  res.status(httpStatus.CREATED).send(newCreate);
});

const getCustomTypeByType = catchAsync(async (req, res) => {
  const newCreate = await masterAccountService.getCustomTypes(
    req.params,
    req.profile
  );

  res.status(httpStatus.CREATED).send(newCreate);
});

const getFilterOptions = catchAsync(async (req, res) => {
  const filterOptions = await masterAccountService.getFilterOptions(
    req.profile
  );
  res.status(httpStatus.OK).send(filterOptions);
});

module.exports = {
  paginateMasterAccounts,
  createMasterAccount,
  createCustomTypes,
  getCustomTypeByType,
  editMasterAccount,
  deleteMasterAccount,
  deleteManyMasterAccount,
  getAccountForOptions,
  getFilterOptions,
};
