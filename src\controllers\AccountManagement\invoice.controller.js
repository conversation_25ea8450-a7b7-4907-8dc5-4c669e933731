const httpStatus = require('http-status');
const pick = require('../../utils/pick');
const ApiError = require('../../utils/ApiError');
const catchAsync = require('../../utils/catchAsync');
const { invoiceService } = require('../../services');

const createInvoice = catchAsync(async (req, res) => {
  const invoice = await invoiceService.createInvoice(req.body, req.profile._id);
  res.status(httpStatus.CREATED).send(invoice);
});

const getInvoices = catchAsync(async (req, res) => {
  const options = pick(req.query, ['page', 'limit']);
  const { searchTerm, type } = req.query;
  const matchStage = {
    profileId: req?.profile?._id,
    archived: { $ne: true },
    type,
  };

  const result = await invoiceService.queryInvoices(
    matchStage,
    options,
    searchTerm
  );

  res.status(httpStatus.OK).send(result);
});

const getInvoice = catchAsync(async (req, res) => {
  const invoice = await invoiceService.getInvoiceById(req.params.id);
  if (!invoice) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Invoice not found');
  }
  res.send(invoice);
});

const updateInvoice = catchAsync(async (req, res) => {
  const invoice = await invoiceService.updateInvoiceById(
    req.params.id,
    req.body
  );
  res.send(invoice);
});

const deleteInvoice = catchAsync(async (req, res) => {
  await invoiceService.deleteInvoiceById(req.params.id);
  res.status(httpStatus.NO_CONTENT).send();
});

const updateInvoiceStatus = catchAsync(async (req, res) => {
  const { status } = req.body;
  const invoice = await invoiceService.updateInvoiceStatus(
    req.params.id,
    status,
    req.body.remark,
    req.user.id
  );
  res.send(invoice);
});

const getInvoicesMetrics = catchAsync(async (req, res) => {
  const metrics = await invoiceService.getInvoicesMetrics(req.profile._id);
  res.send(metrics);
});

const bulkDeleteInvoices = catchAsync(async (req, res) => {
  const { ids } = req.body;
  await invoiceService.bulkDeleteInvoices(ids);
  res.status(httpStatus.NO_CONTENT).send();
});

module.exports = {
  createInvoice,
  getInvoices,
  getInvoice,
  updateInvoice,
  deleteInvoice,
  updateInvoiceStatus,
  getInvoicesMetrics,
  bulkDeleteInvoices,
};
