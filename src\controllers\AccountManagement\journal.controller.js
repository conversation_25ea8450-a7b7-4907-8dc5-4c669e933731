const httpStatus = require('http-status');
const catchAsync = require('../../utils/catchAsync');
const pick = require('../../utils/pick');
const { journalService } = require('../../services');

// const paginateJournal = catchAsync(async (req, res) => {
//   const options = pick(req.query, ['limit', 'page']);
//   const journals = await journalService.getPaginatedJournals(
//     req.query.filter_name,
//     req.query.filter_value,
//     options,
//     req.query.field,
//     req.profile,
//     req.query.type,
//     req.query.searchTerm,
//     req.query.timeRange,
//     req.query.dateRange
//   );
//   res.status(httpStatus.OK).send(journals);
// });

const paginateJournal = catchAsync(async (req, res) => {
  const options = pick(req.query, ['limit', 'page']);
  const pos = await journalService.getPaginatedJournals(
    req.query.filters,
    options,
    req.query.field,
    req.query.type,
    req.profile,
    req.query.searchTerm
  );
  res.status(httpStatus.OK).send(pos);
});

// const paginateJournalDrafts = catchAsync(async (req, res) => {
//   const options = pick(req.query, ['limit', 'page']);
//   const journals = await journalService.getPaginatedJournalDrafts(
//     req.query.filter_name,
//     req.query.filter_value,
//     options,
//     req.query.field,
//     req.profile,
//     req.query.type,
//     req.query.searchTerm,
//     req.query.timeRange,
//     req.query.dateRange
//   );
//   res.status(httpStatus.OK).send(journals);
// });

const paginateJournalDrafts = catchAsync(async (req, res) => {
  const options = pick(req.query, ['limit', 'page']);
  const pos = await journalService.getPaginatedJournalDrafts(
    req.query.filters,
    options,
    req.query.field,
    req.query.type,
    req.profile,
    req.query.searchTerm
  );
  res.status(httpStatus.OK).send(pos);
});

const getJournalById = catchAsync(async (req, res) => {
  const journal = await journalService.getJournalById(req.params);

  res.status(httpStatus.OK).send(journal);
});

const getAllJournals = catchAsync(async (req, res) => {
  const journal = await journalService.getAllJournals(req.profile);

  res.status(httpStatus.OK).send(journal);
});

const createJournal = catchAsync(async (req, res) => {
  const newCreate = await journalService.createJournal(req.body, req.profile);

  res.status(httpStatus.CREATED).send(newCreate);
});

const createJournalDrafts = catchAsync(async (req, res) => {
  const newCreate = await journalService.createJournalDraft(
    req.body,
    req.profile
  );

  res.status(httpStatus.CREATED).send(newCreate);
});

const editJournal = catchAsync(async (req, res) => {
  const newCreate = await journalService.editJournal(req.body, req.profile);

  res.status(httpStatus.OK).send(newCreate);
});

const deleteJournal = catchAsync(async (req, res) => {
  const newCreate = await journalService.deleteJournal(req.body);

  res.status(httpStatus.OK).send(newCreate);
});

const deleteManyJournal = catchAsync(async (req, res) => {
  const newCreate = await journalService.deleteManyJournal(req.body);

  res.status(httpStatus.OK).send(newCreate);
});

const getFilterOptions = catchAsync(async (req, res) => {
  const filterOptions = await journalService.getFilterOptions(req.profile);
  res.status(httpStatus.OK).send(filterOptions);
});

module.exports = {
  paginateJournal,
  createJournal,
  editJournal,
  deleteJournal,
  deleteManyJournal,
  getJournalById,
  paginateJournalDrafts,
  createJournalDrafts,
  getFilterOptions,
  getAllJournals,
};
