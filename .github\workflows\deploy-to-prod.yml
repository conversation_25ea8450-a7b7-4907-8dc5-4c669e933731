name: Pull code on main server
on:
  push:
    branches:
      - main
jobs:
  update-instance-1:
    runs-on: ubuntu-22.04
    env:
      PRIVATE_KEY: ${{ secrets.PROD_SSH_PRIVATE_KEY }}
      HOST1: ${{ secrets.HOST1 }}
      USER_NAME: ${{ secrets.USER_NAME }}
    steps:
      - name: Generate key
        run: echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
      - name: Pull code on ec2
        run: |
          ssh -o StrictHostKeyChecking=no -i private_key ${USER_NAME}@${HOST1} '
              cd code/iot-dashboard-backend &&
              git reset --hard &&
              git switch main &&
              git pull origin main &&
              export NVM_DIR=~/.nvm &&
              source ~/.nvm/nvm.sh &&
              npm ci &&
              pm2 reload optiwise-backend
              '
      - name: Remove key
        run: rm private_key
  update-instance-2:
    runs-on: ubuntu-22.04
    env:
      PRIVATE_KEY: ${{ secrets.PROD_SSH_PRIVATE_KEY }}
      HOST2: ${{ secrets.HOST2 }}
      USER_NAME: ${{ secrets.USER_NAME }}
    steps:
      - name: Generate key
        run: echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
      - name: Pull code on ec2
        run: |
          ssh -o StrictHostKeyChecking=no -i private_key ${USER_NAME}@${HOST2} '
              cd code/iot-dashboard-backend &&
              git reset --hard &&
              git switch main &&
              git pull origin main &&
              export NVM_DIR=~/.nvm &&
              source ~/.nvm/nvm.sh &&
              npm ci &&
              pm2 reload optiwise-backend
              '
      - name: Remove key
        run: rm private_key
  update-instance-3:
    runs-on: ubuntu-22.04
    env:
      PRIVATE_KEY: ${{ secrets.PROD_SSH_PRIVATE_KEY }}
      HOST3: ${{ secrets.HOST3 }}
      USER_NAME: ${{ secrets.USER_NAME }}
    steps:
      - name: Generate key
        run: echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
      - name: Pull code on ec2
        run: |
          ssh -o StrictHostKeyChecking=no -i private_key ${USER_NAME}@${HOST3} '
              cd code/iot-dashboard-backend &&
              git reset --hard &&
              git switch main &&
              git pull origin main &&
              export NVM_DIR=~/.nvm &&
              source ~/.nvm/nvm.sh &&
              npm ci &&
              pm2 reload optiwise-backend
              '
      - name: Remove key
        run: rm private_key
  update-instance-4:
    runs-on: ubuntu-22.04
    env:
      PRIVATE_KEY: ${{ secrets.PROD_SSH_PRIVATE_KEY }}
      HOST4: ${{ secrets.HOST4 }}
      USER_NAME: ${{ secrets.USER_NAME }}
    steps:
      - name: Generate key
        run: echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
      - name: Pull code on ec2
        run: |
          ssh -o StrictHostKeyChecking=no -i private_key ${USER_NAME}@${HOST4} '
              cd code/iot-dashboard-backend &&
              git reset --hard &&
              git switch main &&
              git pull origin main &&
              export NVM_DIR=~/.nvm &&
              source ~/.nvm/nvm.sh &&
              npm ci &&
              pm2 reload optiwise-backend
              '
      - name: Remove key
        run: rm private_key
  update-instance-5:
    runs-on: ubuntu-22.04
    env:
      PRIVATE_KEY: ${{ secrets.PROD_SSH_PRIVATE_KEY }}
      HOST5: ${{ secrets.HOST5 }}
      USER_NAME: ${{ secrets.USER_NAME }}
    steps:
      - name: Generate key
        run: echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
      - name: Pull code on ec2
        run: |
          ssh -o StrictHostKeyChecking=no -i private_key ${USER_NAME}@${HOST5} '
              cd code/iot-dashboard-backend &&
              git reset --hard &&
              git switch main &&
              git pull origin main &&
              export NVM_DIR=~/.nvm &&
              source ~/.nvm/nvm.sh &&
              npm ci &&
              pm2 reload optiwise-backend
              '
      - name: Remove key
        run: rm private_key
