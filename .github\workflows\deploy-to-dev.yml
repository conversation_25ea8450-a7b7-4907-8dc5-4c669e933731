name: Pull code on dev server
on:
  push:
    branches:
      - dev
jobs:
  build-and-upload:
    runs-on: ubuntu-22.04
    env:
      PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
      HOSTNAME: ${{ secrets.SSH_HOST }}
      USER_NAME: ${{ secrets.USER_NAME }}
    steps:
      - name: Pull code on ec2
        run: |
          echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
          ssh -o StrictHostKeyChecking=no -i private_key ${USER_NAME}@${HOSTNAME} '
              # Now we have got the access of EC2 and we will start the deploy .
              cd code/iot-dashboard-backend &&
              git reset --hard &&
              git switch dev &&
              git pull origin dev &&
              export NVM_DIR=~/.nvm &&
              source ~/.nvm/nvm.sh &&
              npm ci &&
              pm2 restart dev-optiwise-backend
              '
