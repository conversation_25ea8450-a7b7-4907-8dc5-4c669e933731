const mongoose = require('mongoose');
const config = require('../../config/config');
const logger = require('../../config/logger');
const { PrefixId } = require('../index');

async function migrate() {
  try {
    await mongoose.connect(config.mongoose.url, config.mongoose.options);
    logger.info('Connected to MongoDB');

    const allPrefixIds = await PrefixId.find({});
    for (const prefixId of allPrefixIds) {
      // remove voucherId from prefixId
      delete prefixId.voucherId;
      prefixId.purchaseVoucherId = [
        {
          Increment_0: 1,
          isUsed: false,
        },
      ];
      prefixId.salesVoucherId = [
        {
          Increment_0: 1,
          isUsed: false,
        },
      ];
      prefixId.paymentVoucherId = [
        {
          Increment_0: 1,
          isUsed: false,
        },
      ];
      prefixId.journalVoucherId = [
        {
          Increment_0: 1,
          isUsed: false,
        },
      ];
      prefixId.receiptVoucherId = [
        {
          Increment_0: 1,
          isUsed: false,
        },
      ];
      await prefixId.save();
      logger.info(`Updated PrefixId: ${prefixId._id}`);
    }

    logger.info('Migration completed successfully');
  } catch (err) {
    logger.error('Error during the migration process:', err);
  } finally {
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  }
}

migrate();
