const mongoose = require('mongoose');
const config = require('../../config/config');
const {
  DepartmentChildNav,
  Department,
  DepartmentNav,
  User,
} = require('../index');
const logger = require('../../config/logger');

const orderedPages = [
  '/accountmanagement/masteraccounts',
  '/accountmanagement/invoices',
  '/accountmanagement/bills',
  '/accountmanagement/voucher',
  '/accountmanagement/journal',
  '/accountmanagement/ledger',
  '/accountmanagement/trialbalance',
  '/accountmanagement/financialreport',
];
const pageToDelete = '/accountmanagement/proformainvoice';
const departmentName = 'accountManagement';
const departmentNavName = 'Account Management';

const cleanupAndReorderNavs = async () => {
  mongoose
    .connect(config.mongoose.url, {
      ...config.mongoose.options,
      useCreateIndex: true,
    })
    .then(async () => {
      try {
        const users = await User.find({});
        const profileIds = users.map((user) => user.profileId);

        const departments = await Department.find({
          profileId: { $in: profileIds },
          name: departmentName,
        });

        for (const department of departments) {
          const departmentNavs = await DepartmentNav.find({
            name: departmentNavName,
            profileId: department.profileId,
          });

          for (const departmentNav of departmentNavs) {
            // Delete unwanted page if exists
            const toDelete = await DepartmentChildNav.findOne({
              cslug: pageToDelete,
              department: department._id,
              departmentNav: departmentNav._id,
              profileId: department.profileId,
            });

            if (toDelete) {
              // Remove reference from DepartmentNav.childNavs
              await DepartmentNav.findByIdAndUpdate(departmentNav._id, {
                $pull: { childNavs: toDelete._id },
              });

              // Delete the actual child nav
              await DepartmentChildNav.findByIdAndDelete(toDelete._id);

              logger.info(`Deleted page: ${toDelete.cname}`);
            }

            //  Reorder remaining child navs
            const allChildNavs = await DepartmentChildNav.find({
              department: department._id,
              departmentNav: departmentNav._id,
              profileId: department.profileId,
            });

            const slugToIdMap = new Map(
              allChildNavs.map((nav) => [nav.cslug, nav._id])
            );

            const newOrderedChildNavs = orderedPages
              .map((slug) => slugToIdMap.get(slug))
              .filter(Boolean);

            if (newOrderedChildNavs.length > 0) {
              departmentNav.childNavs = newOrderedChildNavs;
              await departmentNav.save();
              logger.info(
                `Reordered pages for departmentNav: ${departmentNav.name} (${department.profileId})`
              );
            }
          }

          // Remove access from users
          await User.updateMany(
            {
              profileId: department.profileId,
              pageAccess: pageToDelete,
            },
            {
              $pull: { pageAccess: pageToDelete },
            }
          );
        }

        logger.info('Cleanup and reorder migration completed!');
      } catch (err) {
        logger.error('Migration failed:', err);
      } finally {
        mongoose.disconnect();
      }
    });
};

cleanupAndReorderNavs();
