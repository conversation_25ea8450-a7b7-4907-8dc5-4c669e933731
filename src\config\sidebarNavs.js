const departments = {
  superuser: [
    {
      name: '<PERSON><PERSON>',
      slug: '/settings/aican',
      isSettingPage: true,
      icon: 'RectangleStackIcon',
      id: 'aican',
      childNavs: [
        {
          cname: 'Profiles',
          cslug: '/settings/aican/profiles',
          icon: 'subscriptions',
        },
        {
          cname: 'IoT Devices',
          cslug: '/settings/aican/iotdevices',
          icon: 'subscriptions',
        },
        {
          cname: 'Dashboard',
          cslug: '/settings/aican/dashboard',
          icon: 'dashboard',
        },
        {
          cname: 'Versions',
          cslug: '/settings/aican/versions',
          icon: 'dashboard',
        },
      ],
    },
  ],
  organization: [
    {
      name: 'My Organization',
      slug: '/settings/database',
      isSettingPage: true,
      icon: 'myOrganization',
      id: 'database',
      childNavs: [
        {
          cname: 'Company Profile',
          cslug: '/settings/database/company-profile',
          icon: 'companyProfile',
        },
        {
          cname: 'Admin',
          cslug: '/settings/database/admin',
          icon: 'admin',
        },
        // {
        //   cname: 'Employees',
        //   cslug: '/settings/database/employees',
        //   icon: 'employee',
        // },
        {
          cname: 'Error Codes',
          cslug: '/settings/database/errorcodes',
          icon: 'errorcodes',
        },
      ],
    },
    {
      name: 'Configurations',
      slug: '/settings/config',
      isSettingPage: true,
      id: 'configurations',
      icon: 'configuration',
      childNavs: [
        {
          cname: 'Server',
          cslug: '/settings/config/server',
        },
        {
          cname: 'Activity Logs',
          cslug: '/settings/config/logs',
          icon: 'activityLogs',
        },
        {
          cname: 'Defaults',
          cslug: '/settings/config/defaults',
          icon: 'defaults',
        },
        {
          cname: 'Order Forms',
          cslug: '/settings/config/orderform',
          icon: 'orderForms',
        },
        {
          cname: 'Customer',
          cslug: '/settings/config/customer',
          icon: 'orderForms',
        },
        {
          cname: 'Mail',
          cslug: '/settings/config/email',
          icon: 'mails',
        },
        {
          cname: 'Notification',
          cslug: '/settings/config/notification',
          icon: 'mails',
        },
      ],
    },
    {
      name: 'Departments',
      slug: '/settings/departments',
      isSettingPage: true,
      icon: 'departments',
      id: 'department',
      childNavs: [
        {
          cname: 'Manage Departments',
          cslug: '/settings/departments/managedepartments',
          icon: 'managementDepartment',
        },
        {
          cname: 'Task Manager',
          cslug: '/settings/departments/columns',
          icon: 'column',
        },
      ],
    },
  ],
  production: [
    {
      name: 'Setup',
      slug: '/settings/setup',
      isSettingPage: true,
      icon: 'setUp',
      id: 'setup',
      childNavs: [
        {
          cname: 'Process',
          cslug: '/settings/setup/process',
          icon: 'process',
        },
        {
          cname: 'Calendar',
          cslug: '/settings/setup/calendar',
          icon: 'calendar',
        },
        {
          cname: 'Devices',
          cslug: '/settings/setup/devices',
          icon: 'devices',
        },
        {
          cname: 'Machines',
          cslug: '/settings/setup/machines',
          icon: 'machines',
        },
        {
          cname: 'Analytics',
          cslug: '/settings/setup/analytics',
          icon: 'analytics',
        },
      ],
    },
    {
      name: 'Create',
      slug: '/settings/create',
      isSettingPage: true,
      icon: 'create',
      id: 'create',
      childNavs: [
        {
          cname: 'Production Flow',
          cslug: '/settings/create/productionflow',
          icon: 'productionFlow',
        },
        {
          cname: 'Job Template',
          cslug: '/settings/create/jobtemplate',
          icon: 'jobTemplate',
        },
        {
          cname: 'Template',
          cslug: '/settings/create/template',
          icon: 'jobTemplate',
        },
        {
          cname: 'Forms',
          cslug: '/settings/setup/forms',
          icon: 'forms',
        },
        {
          cname: 'Email Template',
          cslug: '/settings/create/emailtemplate',
          icon: 'emailTemplate',
        },
      ],
    },
    {
      name: 'Analytics',
      slug: '/analytics/production',
      isSettingPage: false,
      icon: 'analytics',
      id: 'analytics',
      childNavs: [
        {
          cname: 'Production',
          cslug: '/analytics/production',
          icon: 'production',
        },
        {
          cname: 'Worker',
          cslug: '/analytics/worker',
          icon: 'worker',
        },
        {
          cname: 'Downtime',
          cslug: '/analytics/downtime',
          icon: 'downtime',
        },
      ],
    },
    {
      name: 'Dashboard',
      slug: '/dashboard/master',
      isSettingPage: false,
      icon: 'dashboard',
      id: 'dashboard',
      childNavs: [
        // {
        //   cname: 'Master Dashboard',
        //   cslug: '/dashboard/master',
        // },
        {
          cname: 'Satellite View',
          cslug: '/dashboard/satelliteview',
          icon: 'satelliteView',
        },
        {
          cname: 'Production Line',
          cslug: '/dashboard/productionline',
          icon: 'productionLine',
        },
        {
          cname: 'Job Performance',
          cslug: '/dashboard/jobperformance',
          icon: 'jobPlanner',
        },
      ],
    },
    {
      name: 'Jobs',
      slug: '/manageprojects',
      isSettingPage: false,
      icon: 'jobs',
      id: 'jobs',
      childNavs: [
        {
          cname: 'Assembly',
          cslug: '/jobs/assembly',
          icon: 'assembly',
        },
        {
          cname: 'Work Order',
          cslug: '/jobs/workorder',
          icon: 'workOrder',
        },
        {
          cname: 'Create Jobs',
          cslug: '/jobs/createjobs',
          icon: 'createJobs',
        },
        {
          cname: 'Manage Jobs',
          cslug: '/jobs/managejobs',
          icon: 'manageJobs',
        },
        {
          cname: 'Job Planner',
          cslug: '/jobs/jobplanner',
          icon: 'jobPlanner',
        },
        {
          cname: 'Control Unit',
          cslug: '/jobs/controlunit',
          icon: 'controlunit',
        },
        {
          cname: 'Outsource Jobs',
          cslug: '/outward',
          icon: 'outsourceJobs',
        },
        {
          cname: 'Inspection Dashboard',
          cslug: '/jobs/inspection',
          icon: 'inspectionDashboard',
        },
        {
          cname: 'NCR Dashboard',
          cslug: '/jobs/ncr',
          icon: 'ncrDashboard',
        },
      ],
    },
    {
      name: 'Reports',
      slug: '/report',
      isSettingPage: false,
      id: 'reports',
      icon: 'reports',
      childNavs: [
        {
          cname: 'Custom Report',
          cslug: '/report/customreport',
          icon: 'customReporter',
        },
        // {
        //   cname: 'PO Report',
        //   cslug: '/poreport',
        // },
        {
          cname: 'Work Order Report',
          cslug: '/report/workorderreport',
          icon: 'workOrder',
        },
      ],
    },
  ],
  inventory: [
    {
      name: 'Inventory',
      slug: '/inventory',
      isSettingPage: false,
      id: 'inventory',
      icon: 'inventory',
      childNavs: [
        {
          cname: 'Assets',
          cslug: '/inventory/assets',
          icon: 'assets',
        },
        {
          cname: 'Stock In',
          cslug: '/inventory/inpage',
          icon: 'stockIn',
        },
        {
          cname: 'Stock Out',
          cslug: '/inventory/outpage',
          icon: 'stockOut',
        },
        {
          cname: 'Real Time',
          cslug: '/inventory/realtime',
          icon: 'realTime',
        },
        {
          cname: 'Internal Stock Transfer',
          cslug: '/inventory/internalstocktransfer',
          icon: 'internalStockTransfer',
        },
        {
          cname: 'Transactions',
          cslug: '/inventory/transactions',
          icon: 'transactions',
        },
        {
          cname: 'Dashboard',
          cslug: '/inventory/dashboard',
          icon: 'inventoryDashboard',
        },
      ],
    },
    {
      name: 'Setup Inventory',
      slug: '/settings/inventory',
      isSettingPage: true,
      icon: 'setUpInventory',
      id: 'setupInventory',
      childNavs: [
        {
          cname: 'Masters',
          cslug: '/settings/inventory/masters',
          icon: 'master',
        },
        {
          cname: 'Products',
          cslug: '/settings/inventory/products',
          icon: 'products',
        },
      ],
    },
  ],
  sales: [
    {
      name: 'Sales Order Management',
      slug: '/salesordermanagement',
      isSettingPage: false,
      id: 'salesordermanagement',
      icon: 'salesOrderManagement',
      childNavs: [
        {
          cname: 'Sales Inquiry',
          cslug: '/salesordermanagement/salesinquirydashboard',
          icon: 'rfq',
        },
        {
          cname: 'Quotation',
          cslug: '/salesordermanagement/quotation',
          icon: 'quotation',
        },
        {
          cname: 'Sales Order',
          cslug: '/salesordermanagement/orders',
          icon: 'salesOrder',
        },
        {
          cname: 'Sales Dashboard',
          cslug: '/salesordermanagement/sales-dashboard',
          icon: 'salesDashboard',
        },
      ],
    },
  ],
  attendance: [
    {
      name: 'Attendance',
      slug: '/attendance',
      isSettingPage: false,
      id: 'attendance',
      icon: 'attendance',
      childNavs: [
        {
          cname: 'Attendance Dashboard',
          cslug: '/attendance/dashboard',
          icon: 'attendanceDashboard',
        },
      ],
    },
  ],
  purchase: [
    {
      name: 'Purchase Management',
      slug: '/purchase',
      isSettingPage: false,
      id: 'purchase',
      icon: 'purchaseManagement',
      childNavs: [
        {
          cname: 'Indent Dashboard',
          cslug: '/purchase/indent',
          icon: 'indentdashboard',
        },
        {
          cname: 'Request for Quotation',
          cslug: '/purchase/requestforquotation',
          icon: 'quotation',
        },
        {
          cname: 'PO Dashboard',
          cslug: '/purchase/po',
          icon: 'po',
        },
      ],
    },
  ],
  dispatch: [
    {
      name: 'Dispatch Management',
      slug: '/dispatch',
      isSettingPage: false,
      icon: 'dispatchManagement',
      childNavs: [
        {
          cname: 'Dispatch Dashboard',
          cslug: '/dispatch/dashboard',
          icon: 'dispatchDashboard',
        },
      ],
    },
  ],
  equipmentManagement: [
    {
      name: 'Maintenance Management',
      slug: '/maintenance',
      isSettingPage: false,
      icon: 'maintainanceManagement',
      childNavs: [
        {
          cname: 'Equipment Management',
          cslug: '/maintenance/equipment',
          icon: 'indentdashboard',
        },
        {
          cname: 'Service Management',
          cslug: '/maintenance/servicemanagement',
          icon: 'indentdashboard',
        },
        {
          cname: 'Maintenance Dashboard',
          cslug: '/maintenance/dashboard',
          icon: 'maintenanceDashboard',
        },
        {
          cname: 'Device Dashboard',
          cslug: '/maintenance/devicedashboard',
          icon: 'deviceDashboard',
        },
      ],
    },
  ],
  accountManagement: [
    {
      name: 'Account Management',
      slug: '/accountmanagement',
      isSettingPage: false,
      icon: 'accountManagement',
      childNavs: [
        {
          cname: 'Master Account',
          cslug: '/accountmanagement/masteraccounts',
          icon: 'masterAccount',
        },
        {
          cname: 'Invoice',
          cslug: '/accountmanagement/invoices',
          icon: 'invoice',
        },
        {
          cname: 'Bills',
          cslug: '/accountmanagement/bills',
          icon: 'bills',
        },
        {
          cname: 'Voucher',
          cslug: '/accountmanagement/voucher',
          icon: 'voucher',
        },
        {
          cname: 'Journal',
          cslug: '/accountmanagement/journal',
          icon: 'journal',
        },
        {
          cname: 'Ledger',
          cslug: '/accountmanagement/ledger',
          icon: 'ledger',
        },
        {
          cname: 'Trial Balance',
          cslug: '/accountmanagement/trialbalance',
          icon: 'trailbalance',
        },
        {
          cname: 'Financial Report',
          cslug: '/accountmanagement/financialreport',
          icon: 'financialreport',
        },
      ],
    },
  ],
  crm: [
    {
      name: 'CRM',
      slug: '/crm',
      isSettingPage: false,
      id: 'crm',
      icon: 'crm',
      childNavs: [
        {
          cname: 'Leads',
          cslug: '/crm/leads',
          icon: 'leads',
        },
        {
          cname: 'Pipeline',
          cslug: '/crm/pipelines',
          icon: 'pipeline',
        },
      ],
    },
  ],
  renewal: [
    {
      name: 'Renewals Management',
      slug: '/renewalsmanagement',
      isSettingPage: false,
      id: 'renewalsmanagement',
      icon: 'renewalsmanagement',
      childNavs: [
        {
          cname: 'Renewals',
          cslug: '/renewalsmanagement/renewals',
          icon: 'renewals',
        },
      ],
    },
  ],
  tickets: [
    {
      name: 'Tickets Management',
      slug: '/ticketsManagement',
      isSettingPage: false,
      id: 'ticketsManagement',
      icon: 'ticketsManagement',
      childNavs: [
        {
          cname: 'Tickets',
          cslug: '/ticketsManagement/tickets',
          icon: 'tickets',
        },
      ],
    },
  ],
  hrms: [
    {
      name: 'HRMS',
      slug: '/hrms',
      isSettingPage: false,
      id: 'hrms',
      icon: 'hrms',
      childNavs: [
        {
          cname: 'Leave Management',
          cslug: '/hrms/leave',
          icon: 'leave',
        },
        {
          cname: 'Attendance Management',
          cslug: '/hrms/attendance',
          icon: 'attendanceManagement',
        },
      ],
    },
  ],
};

module.exports = departments;
