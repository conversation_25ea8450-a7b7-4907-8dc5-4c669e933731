const { Schema, model } = require('mongoose');
const paginate = require('../plugins/paginate.plugin');

const StockOutRequest = new Schema(
  {
    profileId: {
      type: Schema.Types.ObjectId,
      ref: 'Profile',
      index: true,
    },
    part: {
      type: Schema.Types.ObjectId,
      ref: 'Part',
      default: null,
    },
    product: {
      type: Schema.Types.ObjectId,
      ref: 'Product',
      default: null,
    },
    subassembly: {
      type: Schema.Types.ObjectId,
      ref: 'SubAssembly',
      default: null,
    },
    partVariant: {
      type: Schema.Types.ObjectId,
      ref: 'PartVariant',
    },
    productVariant: {
      type: Schema.Types.ObjectId,
      ref: 'ProductVariant',
    },
    deadline: {
      type: Date,
      required: true,
    },
    stockOutQty: {
      type: Number,
      default: 0,
    },
    remainingStockOutQty: {
      type: Number,
      default: 0,
    },
    status: {
      type: String,
      default: 'pending',
      enum: ['pending', 'partially completed', 'completed'],
    },
    workOrder: {
      type: Schema.Types.ObjectId,
      ref: 'WorkOrder',
      default: null,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    comments: String,
    fullStockedOut: {
      type: Boolean,
      default: false,
    },
    salesVoucher: {
      type: Schema.Types.ObjectId,
      ref: 'Voucher',
      default: null,
    },
  },
  {
    timestamps: true,
  }
);

StockOutRequest.plugin(paginate);

module.exports = model('stockoutrequest', StockOutRequest);
