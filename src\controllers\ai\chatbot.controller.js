const httpStatus = require('http-status');
const catchAsync = require('../../utils/catchAsync');
const { chatBotService } = require('../../services/ai');

const getChatResponse = catchAsync(async (req, res) => {
  const chatbotData = await chatBotService.textQuery(
    req.headers.authorization,
    req.body.message,
    req.body.sessionId,
    req.referrerUrl,
    req.user.name
  );
  res.status(httpStatus.OK).send({ chatbotData });
});

const getOCRResponse = catchAsync(async (req, res) => {
  const imageData = await chatBotService.imageDataExtraction(
    req.body,
    req.profile
  );
  res.status(httpStatus.OK).send(imageData);
});

module.exports = { getChatResponse, getOCRResponse };
