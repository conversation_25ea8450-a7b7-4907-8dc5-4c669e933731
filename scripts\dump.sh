
#!/bin/bash

if [ "$#" -lt 3 ]; then
  echo "Usage: $0 <USER_PROFILE_ID> <DB_USERNAME> <DB_PASSWORD>"
  exit 1
fi

USER_PROFILE_ID="$1"
DB_USER="$2"
DB_PASSWORD="$3"
DB_NAME="iotdashboard"  # Replace with your database name
OUTPUT_DIR="./dump"     # Replace with your desired output directory

# Get the list of collections
collections=$(mongosh --tls \
  --host optiwise-db-2.cluster-c9s2sqssimvp.ap-south-1.docdb.amazonaws.com:27017 \
  --tlsCAFile global-bundle.pem \
  --username "${DB_USER}" \
  --password "${DB_PASSWORD}" \
  --retryWrites false \
  --quiet \
  --eval "db.getMongo().getDB('${DB_NAME}').getCollectionNames().join(' ')")

# Loop through each collection and dump data for the specific user
for collection in $collections; do
  echo "Dumping collection: ${collection}"
  sudo mongodump \
    --host optiwise-db-2.cluster-c9s2sqssimvp.ap-south-1.docdb.amazonaws.com:27017 \
    --ssl \
    --sslCAFile global-bundle.pem \
    --username "${DB_USER}" \
    --password "${DB_PASSWORD}" \
    --db "${DB_NAME}" \
    --collection "${collection}" \
    --query "{ \"\$or\": [{ \"profileId\": { \"\$oid\": \"${USER_PROFILE_ID}\" } }, { \"_id\": { \"\$oid\": \"${USER_PROFILE_ID}\" } }] }" \
    --out "${OUTPUT_DIR}" || echo "No data found in ${collection} for profileId ${USER_PROFILE_ID}"
done

echo "Dump complete."

