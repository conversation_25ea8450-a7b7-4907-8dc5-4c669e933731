const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { mediaService } = require('../services');
const dataGenerator = require('../utils/chatBotDataGen');

const deleteMedia = catchAsync(async (req, res) => {
  const { id } = req.params;
  const { model, refId } = req.query;
  const media = await mediaService.deleteMedia(id, model, refId);
  res.status(httpStatus.OK).send({ media });
});

const getAllMedia = catchAsync(async (req, res) => {
  const media = await mediaService.getAllMedia(req.profile);
  res.status(httpStatus.OK).send({ media });
});

const getMediaById = catchAsync(async (req, res) => {
  const { id } = req.params;
  const media = await mediaService.getMediaById(id);
  res.status(httpStatus.OK).send({ media });
});

const getMediaMeta = catchAsync(async (req, res) => {
  const media = await mediaService.getAllMediaWithoutData(req.profile);
  res.status(httpStatus.OK).send({ media });
});

const getMediaByName = catchAsync(async (req, res) => {
  const name = req.query.name;
  const media = await mediaService.getMediaByName(name, req.profile);
  res
    .status(httpStatus.OK)
    .send(
      req.isChatBot
        ? dataGenerator({ media, success: media?._id ? 'true' : 'false' })
        : media
    );
});

const getMediaByIds = catchAsync(async (req, res) => {
  const { ids } = req.body;
  const media = await mediaService.getMediaByObjectIds(ids);
  res.status(httpStatus.OK).send({ media });
});

const kanbanFormMediaHandler = catchAsync(async (req, res) => {
  const { toBeDeleted, toBeAdded } = req.body;

  if (toBeDeleted.length !== 0) {
    await mediaService.deleteMediaByIds(toBeDeleted);
  }
  if (toBeAdded.length !== 0) {
    let isKanban = true;
    const mediaAdded = await mediaService.SaveMedia(
      toBeAdded,
      req.profile,
      isKanban
    );
    res.status(httpStatus.OK).send({ mediaAdded });
  } else if (toBeDeleted.length !== 0) {
    res.status(httpStatus.OK).send({ message: 'Media deleted successfully' });
  } else {
    res
      .status(httpStatus.BAD_REQUEST)
      .send({ message: 'No media to add or delete' });
  }
});

const editMediaById = catchAsync(async (req, res) => {
  const media = await mediaService.editMediaById(req?.params?.id, req?.body);
  res.status(httpStatus.OK).send(media);
});

const uploadSingleMedia = catchAsync(async (req, res) => {
  const media = await mediaService.uploadSingleMedia(req?.body, req?.profile);
  res.status(httpStatus.OK).send(media);
});

module.exports = {
  deleteMedia,
  getAllMedia,
  getMediaById,
  getMediaMeta,
  getMediaByName,
  getMediaByIds,
  kanbanFormMediaHandler,
  editMediaById,
  uploadSingleMedia,
};
