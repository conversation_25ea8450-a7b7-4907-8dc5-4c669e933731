const dotenv = require('dotenv');
const path = require('path');
const Joi = require('joi');
const { networkInterfaces } = require('os');

dotenv.config({ path: path.join(__dirname, '../../.env') });

const envVarsSchema = Joi.object()
  .keys({
    NODE_ENV: Joi.string()
      .valid('production', 'development', 'test')
      .required(),
    PORT_DASHBOARD: Joi.number().default(5000),
    PORT_CONTROLUNIT: Joi.number().default(5001),
    IP_ADDRESS: Joi.string(),
    IS_CLOUD: Joi.string(),
    MQTT_HOST: Joi.string(),
    MQTT_PORT: Joi.string(),
    MQTT_URL: Joi.string(),
    VERSION_MODE: Joi.string(),
    MONGODB_URL: Joi.string().required().description('Mongo DB url'),
    AWS_IOT_CERT: Joi.string(),
    AWS_IOT_PRIVATE: Joi.string(),
    AWS_IOT_CA: Joi.string(),
    AWS_IOT_ENDPOINT: Joi.string(),
    AWS_IOT_POOL_ID: Joi.string(),
    AWS_IOT_REGION: Joi.string(),
    GOOGLE_MAP_API_KEY: Joi.string(),
    JWT_SECRET: Joi.string().required().description('JWT secret key'),
    JWT_ACCESS_EXPIRATION_MINUTES: Joi.number()
      .default(30)
      .description('minutes after which access tokens expire'),
    JWT_REFRESH_EXPIRATION_DAYS: Joi.number()
      .default(30)
      .description('days after which refresh tokens expire'),
    JWT_RESET_PASSWORD_EXPIRATION_MINUTES: Joi.number()
      .default(10)
      .description('minutes after which reset password token expires'),
    JWT_VERIFY_EMAIL_EXPIRATION_MINUTES: Joi.number()
      .default(10)
      .description('minutes after which verify email token expires'),
    SMTP_HOST: Joi.string().description('server that will send the emails'),
    SMTP_PORT: Joi.number().description('port to connect to the email server'),
    SMTP_USERNAME: Joi.string().description('username for email server'),
    SMTP_PASSWORD: Joi.string().description('password for email server'),
    EMAIL_FROM: Joi.string().description(
      'the from field in the emails sent by the app'
    ),
    GET_GST_API: Joi.string(),
  })
  .unknown();

const { value: envVars, error } = envVarsSchema
  .prefs({ errors: { label: 'key' } })
  .validate(process.env);

if (error) {
  throw new Error(`Config validation error: ${error.message}`);
}

const nets = networkInterfaces();

let ipAddress = '';

const wifiNames = [
  'Wi-Fi',
  'wlp',
  'wlo1',
  'WiFi',
  'wlan0',
  'Ethernet',
  'enp1s0',
  'en0',
];

Object.keys(nets).forEach((name) => {
  if (wifiNames.find((wn) => name.startsWith(wn))) {
    Object.keys(nets[name]).forEach((temp) => {
      const net = nets[name][temp];
      if (net.family === 'IPv4') {
        ipAddress = net.address;
      }
    });
  }
});

if (envVars?.IP_ADDRESS) {
  ipAddress = envVars.IP_ADDRESS;
}

module.exports = {
  env: envVars.NODE_ENV,
  dashbboardPort: envVars.PORT_DASHBOARD,
  controlUnitPort: envVars.PORT_CONTROLUNIT,
  mqttUrl:
    envVars?.IS_CLOUD === 'true'
      ? ''
      : envVars.MQTT_URL ||
        `ws://${ipAddress || envVars.MQTT_HOST}:${envVars.MQTT_PORT}`,
  mongoose: {
    url: envVars.MONGODB_URL + (envVars.NODE_ENV === 'test' ? '-test' : ''),
    options: {
      // useCreateIndex: true,
      useNewUrlParser: true,
      useUnifiedTopology: true,
      // useFindAndModify: false,
    },
  },
  jwt: {
    secret: envVars.JWT_SECRET,
    accessExpirationMinutes: envVars.JWT_ACCESS_EXPIRATION_MINUTES,
    refreshExpirationDays: envVars.JWT_REFRESH_EXPIRATION_DAYS,
    resetPasswordExpirationMinutes:
      envVars.JWT_RESET_PASSWORD_EXPIRATION_MINUTES,
    verifyEmailExpirationMinutes: envVars.JWT_VERIFY_EMAIL_EXPIRATION_MINUTES,
  },
  firebase: {
    apiKey: envVars.API_KEY,
    authDomain: envVars.AUTH_DOMAIN,
    databaseURL: envVars.DATABASE_URL,
    projectId: envVars.PROJECT_ID,
    storageBucket: envVars.STORAGE_BUCKET,
    messagingSenderId: envVars.MESSAGING_SENDER_ID,
    appId: envVars.APP_ID,
    measurementId: envVars.MEASURMENT_ID,
  },
  awsIotDetails: {
    awsIotEndpoint: envVars?.AWS_IOT_ENDPOINT,
    awsIotPoolId: envVars?.AWS_IOT_POOL_ID,
    awsIotRegion: envVars?.AWS_IOT_REGION,
  },
  ipAddress,
  cuUrl: envVars.CU_URL,
  googleMapApiKey: envVars.GOOGLE_MAP_API_KEY,
  versionMode: envVars.VERSION_MODE,
  gstApi: envVars.GET_GST_API,
};
