const httpStatus = require('http-status');
const ApiError = require('../../utils/ApiError');
const { Voucher } = require('../../models');
const {
  parseFiltersSafely,
  convertSetToOptions,
} = require('../../helperfunction');
const createLog = require('../../utils/createLog');
const {
  Product,
  StockOutReq,
  InPage,
  Order,
  Store,
  Transactions,
} = require('../../models/inventory');
const { generateId, postPrefixId } = require('../../utils/prefixIdFunction');

// Create stock out request from sales voucher
const createStockOutRequestFromSalesVoucher = async (
  stockOutItems,
  voucherId,
  profile,
  userId
) => {
  const stockOutRequests = [];

  for (const item of stockOutItems) {
    const product = await Product.findOne({
      // eslint-disable-next-line security/detect-non-literal-regexp, prefer-template
      name: {
        $regex: new RegExp(
          '^' + item.productName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '$'
        ),
        $options: 'i',
      },
      profileId: profile._id,
    });

    if (!product) {
      throw new ApiError(
        httpStatus.NOT_FOUND,
        `Product "${item.productName}" not found`
      );
    }

    // Create stock out request
    const stockOutRequest = {
      profileId: profile._id,
      product: product._id,
      stockOutQty: item.quantity,
      remainingStockOutQty: item.quantity,
      deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      status: 'pending',
      createdBy: userId,
      salesVoucher: voucherId,
      comments: `Stock out request created from sales voucher`,
    };

    stockOutRequests.push(stockOutRequest);
  }

  await StockOutReq.insertMany(stockOutRequests);
};

// Direct stock out from sales voucher
const directStockOutFromSalesVoucher = async (
  stockOutItems,
  profile,
  userId
) => {
  const doc = { isNew: true, profileId: profile._id, idData: {} };

  const { status, id: chalaanNo, message } = await generateId(doc, 'chalanId');
  if (status === 'error') throw new ApiError(httpStatus.BAD_REQUEST, message);

  const transactionDocs = [];

  for (const item of stockOutItems) {
    const findedItem = await Product.findOne({
      // eslint-disable-next-line security/detect-non-literal-regexp, prefer-template
      name: {
        $regex: new RegExp(
          '^' + item.productName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '$'
        ),
        $options: 'i',
      },
      profileId: profile._id,
    });

    if (!findedItem) {
      throw new ApiError(
        httpStatus.NOT_FOUND,
        `Product "${item.productName}" not found`
      );
    }

    // Check if enough quantity is available
    if (findedItem.quantity < item.quantity) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        `Insufficient quantity for product "${item.productName}". Available: ${findedItem.quantity}, Required: ${item.quantity}`
      );
    }

    // Find inpages for this product and store
    const inpages = await InPage.find({
      product: findedItem._id,
      store: item.storeId,
      remainingQuantity: { $gt: 0 },
    }).sort({ createdAt: 1 });

    if (inpages.length === 0) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        `No available stock found for product "${item.productName}" in the specified store`
      );
    }

    let quantityToDecrease = item.quantity;

    for (const inpage of inpages) {
      if (quantityToDecrease <= 0) break;

      const usedQty = Math.min(inpage.remainingQuantity, quantityToDecrease);

      findedItem.quantity -= usedQty;

      const orderPayload = {
        profileId: profile._id,
        chalaanNo,
        returnable: {
          status: 'Pending',
          stockedInQuantity: 0,
          returnQuantity: 0,
          isReturnable: false,
          stockedInDate: null,
        },
        receivedBy: userId,
        comment: 'Sales Voucher Stock Out',
        workerId: null,
        usegeId: null,
        inpageId: inpage._id,
        quantity: usedQty,
        store: item.storeId,
        currentRealTimeQuantity: findedItem.quantity,
        remainingQuantity: inpage.remainingQuantity,
        netQuantity: findedItem.quantity,
      };

      const newOrder = new Order(orderPayload);
      await newOrder.save();

      // Update inpage
      inpage.remainingQuantity -= usedQty;
      inpage.isUsed = true;
      await inpage.save();

      // Create transaction
      const store = await Store.findById(item.storeId);
      const transaction = new Transactions({
        object: findedItem.name,
        from: store?.name || 'Unknown Store',
        to: 'Sales',
        batch: null,
        lot: null,
        netQuantity: findedItem.quantity,
        quantity: usedQty,
        time: new Date(),
        profileId: profile._id,
        inPageId: inpage._id,
        actionBy: userId,
        transferUom: '',
      });

      transactionDocs.push(transaction);
      quantityToDecrease -= usedQty;
    }

    // Update store-wise quantity
    const storeWise = findedItem?.storeWiseQuantity?.find(
      (store) => store.store.toString() === item.storeId.toString()
    );
    if (!storeWise || storeWise.remainingQuantity < item.quantity) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Not enough store stock.');
    }
    storeWise.remainingQuantity -= item.quantity;

    // Save the item with updated quantities
    await findedItem.save();
  }

  if (transactionDocs.length) {
    await Transactions.insertMany(transactionDocs);
  }

  await postPrefixId(doc, 'chalanId');
};

const getPaginatedVouchers = async (
  queryFilters,
  options,
  field,
  type,
  profile,
  searchTerm,
  voucherType
) => {
  try {
    const filters = { profileId: profile?._id, voucherType };
    const parsedFilters = parseFiltersSafely(queryFilters) || [];
    if (parsedFilters?.length > 0) {
      parsedFilters.forEach((filter) => {
        const { path, value, isDate } = filter;
        if (isDate) {
          filters[path] = {
            $gte: new Date(value[0]),
            $lte: new Date(value[1]),
          };
          return;
        }
        filters[path] = { $in: value };
      });
    }

    const filteredInfo = [];
    if (searchTerm !== undefined && searchTerm?.trim() !== '') {
      const voucherId = {
        voucherId: { $regex: `^${searchTerm}`, $options: 'i' },
      };
      const remark = {
        remark: { $regex: `^${searchTerm}`, $options: 'i' },
      };
      filteredInfo.push(voucherId);
      filteredInfo.push(remark);
    }

    if (filteredInfo?.length > 0) {
      filters.$or = filteredInfo;
    }
    // const sortOption = field
    //   ? { [field]: type === 'desc' ? -1 : 1 }
    //   : { createdAt: -1 };
    let sortBy = 'createdAt:desc';
    if (type === 'asc') {
      sortBy = 'createdAt:aesc';
    }

    const data = await Voucher.paginate(filters, {
      ...options,
      sortBy,
      populate: [
        {
          path: 'purchaseVoucherData salesVoucherData journalVoucherData receiptVoucherData paymentVoucherData',
          populate: {
            path: 'vendor customer ledgerType productTableFormat accounts journal',
            populate: {
              path: 'accounts',
            },
          },
        },
      ],
    });
    return data;
  } catch (error) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Error Fetching Sales Order');
  }
};

const getVoucherById = async (id, profile) => {
  const voucher = await Voucher.findOne({
    _id: id,
    profileId: profile?._id,
  }).populate({
    path: 'purchaseVoucherData salesVoucherData journalVoucherData receiptVoucherData paymentVoucherData',
    populate: {
      path: 'vendor customer ledgerType productTableFormat accounts journal',
      populate: {
        path: 'accounts',
      },
    },
  });
  if (!voucher) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Ledger not found');
  }
  return voucher;
};

const createVoucher = async (data, profile, userId) => {
  const {
    idData = {},
    voucherType,
    date,
    remark,
    remarks,
    accounts,
    journal,
    referenceId,
    additionalFields,
    paymentMode,
    paidTo,
    amount,
    items,
    vendor,
    customer,
    ledgerType,
    billTo,
    from,
    charges,
    columnVisibility,
    chargesVisibility,
    productTableFormat,
    stockOutOperation,
    stockOutItems,
    ...rest
  } = data;

  const allowedTypes = [
    'purchaseVoucher',
    'salesVoucher',
    'paymentVoucher',
    'journalVoucher',
    'receiptVoucher',
  ];
  if (!allowedTypes.includes(voucherType)) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      `Invalid voucherType: ${voucherType}`
    );
  }

  const pick = (src, keys) =>
    keys.reduce((acc, k) => {
      if (src[k] !== undefined) acc[k] = src[k];
      return acc;
    }, {});

  const payload = {
    profileId: profile?._id,
    idData,
    voucherType,
    date,
    remarks: remarks ?? remark,
  };

  if (voucherType === 'receiptVoucher') {
    payload.receiptVoucherData = {
      ...pick(
        {
          billTo,
          from,
          items,
          additionalFields,
          charges,
          columnVisibility,
          chargesVisibility,
          productTableFormat,
        },
        [
          'billTo',
          'from',
          'items',
          'additionalFields',
          'charges',
          'columnVisibility',
          'chargesVisibility',
          'productTableFormat',
        ]
      ),
      ...rest,
    };
    // delete other vouchers data
    delete payload.purchaseVoucherData;
    delete payload.salesVoucherData;
    delete payload.journalVoucherData;
    delete payload.paymentVoucherData;
  } else if (voucherType === 'paymentVoucher') {
    payload.paymentVoucherData = {
      ...pick({ ledgerType, vendor, customer, paymentMode, paidTo, amount }, [
        'ledgerType',
        'vendor',
        'customer',
        'paymentMode',
        'paidTo',
        'amount',
      ]),
      ...rest,
    };
    // delete other vouchers data
    delete payload.purchaseVoucherData;
    delete payload.salesVoucherData;
    delete payload.journalVoucherData;
    delete payload.receiptVoucherData;
  } else if (voucherType === 'journalVoucher') {
    payload.journalVoucherData = {
      ...pick({ accounts, journal, referenceId, additionalFields }, [
        'accounts',
        'journal',
        'referenceId',
        'additionalFields',
      ]),
      ...rest,
    };
    // delete other vouchers data
    delete payload.purchaseVoucherData;
    delete payload.salesVoucherData;
    delete payload.paymentVoucherData;
    delete payload.receiptVoucherData;
  } else if (voucherType === 'salesVoucher') {
    payload.salesVoucherData = {
      ...pick(
        {
          items,
          customer,
          vendor,
          ledgerType,
          additionalFields,
          charges,
          columnVisibility,
          chargesVisibility,
          productTableFormat,
        },
        [
          'items',
          'customer',
          'vendor',
          'ledgerType',
          'additionalFields',
          'charges',
          'columnVisibility',
          'chargesVisibility',
          'productTableFormat',
        ]
      ),
      ...rest,
    };
    // delete other vouchers data
    delete payload.purchaseVoucherData;
    delete payload.paymentVoucherData;
    delete payload.journalVoucherData;
    delete payload.receiptVoucherData;
  } else if (voucherType === 'purchaseVoucher') {
    payload.purchaseVoucherData = {
      ...pick(
        {
          items,
          vendor,
          ledgerType,
          additionalFields,
          charges,
          columnVisibility,
          chargesVisibility,
          productTableFormat,
        },
        [
          'items',
          'vendor',
          'ledgerType',
          'additionalFields',
          'charges',
          'columnVisibility',
          'chargesVisibility',
          'productTableFormat',
        ]
      ),
      ...rest,
    };
    // delete other vouchers data
    delete payload.paymentVoucherData;
    delete payload.journalVoucherData;
    delete payload.receiptVoucherData;
    delete payload.salesVoucherData;
  }
  const voucher = new Voucher(payload);
  const res = await voucher.save();

  const idField = `${voucherType}Id`;
  await createLog('CREATE', idField, 'Voucher', res);

  // Handle stock out operations for sales vouchers
  if (voucherType === 'salesVoucher') {
    if (!stockOutOperation || !stockOutItems || stockOutItems.length === 0) {
      return;
    }

    if (stockOutOperation === 'stockOutRequest') {
      await createStockOutRequestFromSalesVoucher(
        stockOutItems,
        res._id,
        profile,
        userId
      );
    } else if (stockOutOperation === 'directStockOut') {
      await directStockOutFromSalesVoucher(stockOutItems, profile, userId);
    }
  }

  return res;
};

const editVoucher = async (data) => {
  const { id, updateData } = data;
  const voucher = await Voucher.findById(id);
  const updatedAccount = await Voucher.findByIdAndUpdate(id, {
    ...updateData,
    isDraft: false,
  });

  const updateIdField = `${voucher?.voucherType}Id`;
  await createLog('UPDATE', updateIdField, 'Voucher', voucher);

  return updatedAccount;
};

const deleteVoucher = async (data) => {
  const { id } = data;
  const voucher = await Voucher.findById(id);
  const updatedAccount = await Voucher.findByIdAndDelete(id);

  const deleteIdField = `${voucher?.voucherType}Id`;
  await createLog('DELETE', deleteIdField, 'Voucher', voucher);
  return updatedAccount;
};

const deleteManyVoucher = async (data) => {
  const { ids } = data;
  if (!Array.isArray(ids) || ids.length === 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid or empty ids array');
  }
  const result = await Voucher.deleteMany({ _id: { $in: ids } });
  return result;
};

const getFilterOptions = async (profile, voucherType) => {
  const vouchers = await Voucher.find({
    profileId: profile?._id,
    voucherType,
  });
  let filterSet = {
    voucherId: new Set(),
    ledgerType: new Set(),
    remark: new Set(),
  };
  vouchers.forEach((order) => {
    if (order.voucherId) filterSet.voucherId.add(order.voucherId);
    if (order.ledgerType) filterSet.ledgerType.add(order.ledgerType);
    if (order.remark) filterSet.remark.add(order.remark);
  });

  return {
    voucherId: convertSetToOptions(filterSet.voucherId),
    ledgerType: convertSetToOptions(filterSet.ledgerType),
    remark: convertSetToOptions(filterSet.remark),
  };
};

module.exports = {
  getPaginatedVouchers,
  createVoucher,
  deleteVoucher,
  deleteManyVoucher,
  getVoucherById,
  editVoucher,
  getFilterOptions,
};
