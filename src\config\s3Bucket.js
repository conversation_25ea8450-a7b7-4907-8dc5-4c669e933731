const {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
  DeleteObjectsCommand,
} = require('@aws-sdk/client-s3');
const detect = require('detect-file-type');
const fs = require('fs');
const { removeFileFromUploads } = require('../utils/convertHtmlToPdf');

const allowS3Upload = process.env.ALLOW_S3_UPLOAD;
const awsBucketName = process.env.AWS_BUCKET_NAME;
const awsBucketRegion = process.env.AWS_BUCKET_REGION;
const awsBucketAccessKey = process.env.AWS_BUCKET_ACCESS_KEY;
const awsBucketSecretKey = process.env.AWS_BUCKET_SECRET_KEY;

const s3 = new S3Client({
  region: awsBucketRegion,
  credentials: {
    accessKeyId: awsBucketAccessKey,
    secretAccessKey: awsBucketSecretKey,
  },
});

/**
 * Uploads to s3 bucket
 *
 * @param {string} fileName name of the media file
 * @param {string} type media content type
 * @param {string} data media data url
 * @returns res of uploaded object or null if the s3 upload is disabled
 */
const uploadToS3 = async (fileName, type, data) => {
  if (
    allowS3Upload === 'true' &&
    awsBucketName &&
    awsBucketRegion &&
    awsBucketAccessKey &&
    awsBucketSecretKey &&
    data?.startsWith('data:')
  ) {
    const base64Data = data.split(',')[1].trim();
    const buffer = Buffer.from(base64Data, 'base64');

    const command = new PutObjectCommand({
      Bucket: awsBucketName,
      Key: `public/${fileName}`,
      ContentType: type,
      Body: buffer,
    });

    const res = await s3.send(command);

    return res;
  }
  return null;
};

/**
 * Delete single object from s3 bucket
 *
 * @param {string} key key of the object to delete
 * @returns res of deleted object or null if the s3 upload is disabled
 */
const deleteObjectFromS3 = async (key) => {
  if (
    allowS3Upload === 'true' &&
    awsBucketName &&
    awsBucketRegion &&
    awsBucketAccessKey &&
    awsBucketSecretKey
  ) {
    const command = new DeleteObjectCommand({
      Bucket: awsBucketName,
      Key: key,
    });

    const res = await s3.send(command);

    return res;
  }
  return null;
};

/**
 * Delete multiple objects from s3 bucket
 *
 * @param {{Key:string}[]} keys array of object containing strings
 * @returns res of deleted object or null if the s3 upload is disabled
 */
const deleteObjectsFromS3 = async (keys) => {
  if (
    allowS3Upload === 'true' &&
    awsBucketName &&
    awsBucketRegion &&
    awsBucketAccessKey &&
    awsBucketSecretKey
  ) {
    const command = new DeleteObjectsCommand({
      Bucket: awsBucketName,
      Delete: {
        Objects: keys,
      },
    });

    const res = await s3.send(command);

    return res;
  }
  return null;
};

/**
 * Generate unique file name
 *
 * @param {string} name media file name
 * @returns unique file name
 */
const generateFileName = (name) => {
  const splitName = name.replaceAll(' ', '_').split('.');

  const fileName =
    splitName?.length === 1
      ? `${splitName?.[0]}_${Date.now()}`
      : `${splitName
          .splice(0, splitName?.length - 1)
          .join('_')}_${Date.now()}.${splitName[splitName?.length - 1]}`;

  return fileName;
};

/**
 * Genereate aws object url
 *
 * @param {string} name name of media file
 * @returns aws object url
 */
const generateUrl = (name) => {
  return `https://${awsBucketName}.s3.${awsBucketRegion}.amazonaws.com/public/${name}`;
};

/**
 * Upload to s3 using fs
 *
 * @param {string} fileName name of the file from uploads folder
 * @returns res of uploaded object or null if the s3 upload is disabled
 */
const uploadToS3Usingfs = async (fileName) => {
  if (
    allowS3Upload === 'true' &&
    awsBucketName &&
    awsBucketRegion &&
    awsBucketAccessKey &&
    awsBucketSecretKey &&
    fileName
  ) {
    // eslint-disable-next-line security/detect-non-literal-fs-filename
    const buffer = fs.readFileSync(`uploads/${fileName}`);
    let type = '';
    detect.fromBuffer(buffer, function (_err, result) {
      type = result?.mime;
    });

    const command = new PutObjectCommand({
      Bucket: awsBucketName,
      Key: `public/${fileName}`,
      ContentType: type,
      Body: buffer,
    });

    const res = await s3.send(command);

    removeFileFromUploads(fileName);

    return res;
  }
  return null;
};

module.exports = {
  uploadToS3,
  deleteObjectFromS3,
  deleteObjectsFromS3,
  generateFileName,
  generateUrl,
  uploadToS3Usingfs,
};
