name: Auto Assign Reviewers

on:
  pull_request:
    branches:
      - pre_dev
    types: [opened, ready_for_review, reopened]

jobs:
  assign-reviewers:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/github-script@v7
        with:
          script: |
            const groupA = ['bikashd003', 'venteury', 'gr8uvaiz'];
            const groupB = ['Mistry-Shu<PERSON>m', 'Rishav-264'];

            const prAuthor = context.payload.pull_request.user.login;

            function getRandomExcluding(arr, exclude) {
              const filtered = arr.filter(user => user !== exclude);
              if (filtered.length === 0) {
                throw new Error(`No valid reviewers left in group after excluding PR author (${exclude})`);
              }
              return filtered[Math.floor(Math.random() * filtered.length)];
            }
            const reviewerA = getRandomExcluding(groupA, prAuthor);
            const reviewerB = getRandomExcluding(groupB, prAuthor);
            const reviewers = Array.from(new Set([reviewerA, reviewerB])).filter(Boolean);

            console.log(`Assigning reviewers: ${reviewers.join(', ')}`);

            await github.rest.pulls.requestReviewers({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.payload.pull_request.number,
              reviewers,
            });
